<svg viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .person { fill: #1168BD; stroke: #0E4F8C; stroke-width: 2; rx: 15; }
      .system { fill: #999999; stroke: #6B6B6B; stroke-width: 2; rx: 10; }
      .external-system { fill: #555555; stroke: #333333; stroke-width: 2; rx: 10; }
      .container { fill: #438DD5; stroke: #2E6DA4; stroke-width: 2; rx: 8; }
      .text-white { fill: white; font-family: Arial, sans-serif; font-size: 12px; }
      .text-black { fill: #333333; font-family: Arial, sans-serif; font-size: 12px; }
      .text-small { fill: #333333; font-family: Arial, sans-serif; font-size: 10px; }
      .relationship { stroke: #333333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .title-text { fill: #333333; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333333" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1400" height="1000" fill="#F8F9FA"/>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title-text" font-size="20">
    C4 Model - Sistema de Análise de Logs Kubernetes
  </text>
  <text x="700" y="50" text-anchor="middle" class="text-small">
    Context & Container Diagram
  </text>
  
  <!-- Level 1: System Context -->
  <text x="70" y="90" class="title-text">System Context</text>
  
  <!-- Person/Actor -->
  <rect x="100" y="120" width="180" height="100" class="person"/>
  <text x="190" y="150" text-anchor="middle" class="text-white" font-weight="bold">DevOps Engineer</text>
  <text x="190" y="170" text-anchor="middle" class="text-white">Monitora aplicações</text>
  <text x="190" y="185" text-anchor="middle" class="text-white">Kubernetes e analisa</text>
  <text x="190" y="200" text-anchor="middle" class="text-white">logs com IA</text>
  
  <!-- Main System -->
  <rect x="450" y="100" width="200" height="140" class="system"/>
  <text x="550" y="135" text-anchor="middle" class="text-white" font-weight="bold">Sistema de Análise</text>
  <text x="550" y="150" text-anchor="middle" class="text-white">de Logs K8s</text>
  <text x="550" y="175" text-anchor="middle" class="text-white">Coleta logs do Kubernetes,</text>
  <text x="550" y="190" text-anchor="middle" class="text-white">processa com IA e</text>
  <text x="550" y="205" text-anchor="middle" class="text-white">disponibiliza análises</text>
  <text x="550" y="220" text-anchor="middle" class="text-white">[Python, AWS Lambda]</text>
  
  <!-- External Systems -->
  <rect x="800" y="100" width="160" height="80" class="external-system"/>
  <text x="880" y="130" text-anchor="middle" class="text-white" font-weight="bold">Rancher</text>
  <text x="880" y="150" text-anchor="middle" class="text-white">Gerencia clusters</text>
  <text x="880" y="165" text-anchor="middle" class="text-white">Kubernetes</text>
  
  <rect x="800" y="200" width="160" height="80" class="external-system"/>
  <text x="880" y="230" text-anchor="middle" class="text-white" font-weight="bold">Genial AI</text>
  <text x="880" y="250" text-anchor="middle" class="text-white">Processa e analisa</text>
  <text x="880" y="265" text-anchor="middle" class="text-white">logs com IA</text>
  
  <!-- Relationships - Context Level -->
  <line x1="280" y1="170" x2="450" y2="170" class="relationship"/>
  <text x="360" y="165" class="text-small">Solicita análise de logs</text>
  
  <line x1="650" y1="140" x2="800" y2="140" class="relationship"/>
  <text x="720" y="135" class="text-small">Busca logs</text>
  
  <line x1="650" y1="200" x2="800" y2="240" class="relationship"/>
  <text x="720" y="215" class="text-small">Processa logs</text>
  
  <!-- Level 2: Container Diagram -->
  <text x="70" y="340" class="title-text">Container Diagram</text>
  
  <!-- API Container -->
  <rect x="100" y="370" width="160" height="100" class="container"/>
  <text x="180" y="400" text-anchor="middle" class="text-white" font-weight="bold">API Gateway</text>
  <text x="180" y="420" text-anchor="middle" class="text-white">Expõe endpoints REST</text>
  <text x="180" y="435" text-anchor="middle" class="text-white">para análise e</text>
  <text x="180" y="450" text-anchor="middle" class="text-white">recuperação de logs</text>
  <text x="180" y="465" text-anchor="middle" class="text-small">[AWS API Gateway]</text>
  
  <!-- Processing Containers -->
  <rect x="300" y="370" width="140" height="80" class="container"/>
  <text x="370" y="395" text-anchor="middle" class="text-white" font-weight="bold">Pod Manager</text>
  <text x="370" y="410" text-anchor="middle" class="text-white">Lista pods e</text>
  <text x="370" y="425" text-anchor="middle" class="text-white">extrai logs</text>
  <text x="370" y="440" text-anchor="middle" class="text-small">[AWS Lambda]</text>
  
  <rect x="300" y="470" width="140" height="80" class="container"/>
  <text x="370" y="495" text-anchor="middle" class="text-white" font-weight="bold">Log Processor</text>
  <text x="370" y="510" text-anchor="middle" class="text-white">Envia logs para</text>
  <text x="370" y="525" text-anchor="middle" class="text-white">processamento</text>
  <text x="370" y="540" text-anchor="middle" class="text-small">[AWS Lambda]</text>
  
  <rect x="470" y="370" width="140" height="80" class="container"/>
  <text x="540" y="395" text-anchor="middle" class="text-white" font-weight="bold">AI Analyzer</text>
  <text x="540" y="410" text-anchor="middle" class="text-white">Processa logs</text>
  <text x="540" y="425" text-anchor="middle" class="text-white">com IA</text>
  <text x="540" y="440" text-anchor="middle" class="text-small">[AWS Lambda]</text>
  
  <rect x="470" y="470" width="140" height="80" class="container"/>
  <text x="540" y="495" text-anchor="middle" class="text-white" font-weight="bold">URL Generator</text>
  <text x="540" y="510" text-anchor="middle" class="text-white">Gera URLs</text>
  <text x="540" y="525" text-anchor="middle" class="text-white">presigned S3</text>
  <text x="540" y="540" text-anchor="middle" class="text-small">[AWS Lambda]</text>
  
  <!-- Data Containers -->
  <rect x="650" y="370" width="140" height="80" class="container"/>
  <text x="720" y="395" text-anchor="middle" class="text-white" font-weight="bold">Message Queue</text>
  <text x="720" y="410" text-anchor="middle" class="text-white">Processa requisições</text>
  <text x="720" y="425" text-anchor="middle" class="text-white">assíncronas</text>
  <text x="720" y="440" text-anchor="middle" class="text-small">[AWS SQS]</text>
  
  <rect x="650" y="470" width="140" height="80" class="container"/>
  <text x="720" y="495" text-anchor="middle" class="text-white" font-weight="bold">Log Storage</text>
  <text x="720" y="510" text-anchor="middle" class="text-white">Armazena logs</text>
  <text x="720" y="525" text-anchor="middle" class="text-white">e análises</text>
  <text x="720" y="540" text-anchor="middle" class="text-small">[AWS S3]</text>
  
  <rect x="820" y="370" width="140" height="80" class="container"/>
  <text x="890" y="395" text-anchor="middle" class="text-white" font-weight="bold">Secrets Store</text>
  <text x="890" y="410" text-anchor="middle" class="text-white">Armazena tokens</text>
  <text x="890" y="425" text-anchor="middle" class="text-white">e credenciais</text>
  <text x="890" y="440" text-anchor="middle" class="text-small">[AWS Secrets Manager]</text>
  
  <!-- Container Relationships -->
  <line x1="260" y1="420" x2="300" y2="410" class="relationship"/>
  <text x="275" y="410" class="text-small" font-size="9">invoca</text>
  
  <line x1="260" y1="440" x2="300" y2="510" class="relationship"/>
  <text x="275" y="475" class="text-small" font-size="9">invoca</text>
  
  <line x1="440" y1="410" x2="470" y2="410" class="relationship"/>
  <text x="450" y="405" class="text-small" font-size="9">usa</text>
  
  <line x1="440" y1="510" x2="470" y2="510" class="relationship"/>
  <text x="450" y="505" class="text-small" font-size="9">usa</text>
  
  <line x1="610" y1="410" x2="650" y2="410" class="relationship"/>
  <text x="625" y="405" class="text-small" font-size="9">usa</text>
  
  <line x1="610" y1="510" x2="650" y2="510" class="relationship"/>
  <text x="625" y="505" class="text-small" font-size="9">usa</text>
  
  <line x1="540" y1="370" x2="890" y2="370" class="relationship"/>
  <text x="710" y="365" class="text-small" font-size="9">busca credenciais</text>
  
  <!-- Technology Stack -->
  <rect x="100" y="600" width="860" height="200" fill="white" stroke="#333333" stroke-width="1" rx="5"/>
  <text x="530" y="625" text-anchor="middle" class="title-text">Technology Stack & Key Components</text>
  
  <!-- Infrastructure -->
  <text x="120" y="655" class="text-black" font-weight="bold">Infrastructure (AWS):</text>
  <text x="130" y="675" class="text-black">• API Gateway - Exposição de endpoints REST</text>
  <text x="130" y="690" class="text-black">• Lambda Functions - Processamento serverless</text>
  <text x="130" y="705" class="text-black">• SQS - Fila para processamento assíncrono</text>
  <text x="130" y="720" class="text-black">• S3 - Armazenamento de logs e análises</text>
  <text x="130" y="735" class="text-black">• Secrets Manager - Gerenciamento de credenciais</text>
  <text x="130" y="750" class="text-black">• IAM - Controle de acesso e permissões</text>
  
  <!-- External Services -->
  <text x="500" y="655" class="text-black" font-weight="bold">External Services:</text>
  <text x="510" y="675" class="text-black">• Rancher - Gerenciamento de clusters K8s</text>
  <text x="510" y="690" class="text-black">• Kubernetes API - Acesso aos pods e logs</text>
  <text x="510" y="705" class="text-black">• Genial AI - Análise inteligente de logs</text>
  
  <!-- Runtime -->
  <text x="120" y="775" class="text-black" font-weight="bold">Runtime & Languages:</text>
  <text x="130" y="790" class="text-black">• Python 3.12 - Linguagem principal</text>
  
  <!-- Key Libraries -->
  <text x="500" y="775" class="text-black" font-weight="bold">Key Libraries:</text>
  <text x="510" y="790" class="text-black">• kubernetes - Cliente K8s Python</text>
  
  <!-- Data Flow Indicators -->
  <rect x="100" y="820" width="860" height="160" fill="#F0F8FF" stroke="#4682B4" stroke-width="1" rx="5"/>
  <text x="530" y="845" text-anchor="middle" class="title-text">Data Flow Patterns</text>
  
  <text x="120" y="870" class="text-black" font-weight="bold">1. Synchronous Flow (List Pods):</text>
  <text x="130" y="885" class="text-black">API Gateway → Lambda → Rancher API → Kubernetes API → Response</text>
  
  <text x="120" y="910" class="text-black" font-weight="bold">2. Asynchronous Flow (Log Analysis):</text>
  <text x="130" y="925" class="text-black">API Gateway → Lambda → SQS → Background Lambda → Rancher → K8s → Genial AI → S3</text>
  
  <text x="120" y="950" class="text-black" font-weight="bold">3. File Access Flow:</text>
  <text x="130" y="965" class="text-black">Client → API Gateway → Lambda → S3 Presigned URL → Direct S3 Access</text>
</svg>