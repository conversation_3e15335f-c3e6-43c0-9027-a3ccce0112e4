# AWS Service Python CPE CloudIn Kubernetes

Este projeto implementa uma solução serverless para gerenciamento e monitoramento de clusters Kubernetes através da AWS, integrando com Rancher para operações de cluster e fornecendo APIs para análise de logs e pods.

## 📋 Visão Geral

O sistema oferece uma API REST para:

- Listagem de pods em clusters Kubernetes
- Extração e análise de logs de aplicações
- Geração de URLs pré-assinadas para armazenamento de logs
- Integração com Rancher para gerenciamento de clusters
- Processamento assíncrono de logs via SQS

## 🏗️ Arquitetura

### Diagrama de Arquitetura AWS

![AWS Architecture](./aws_architecture.svg)

### Diagrama C4 - Visão Geral

![C4 Architecture](./c4_architecture.svg)

### Diagrama C4 - Kubernetes

![K8s C4 Diagrams](./k8s_c4_diagrams.svg)

### Arquitetura Simplificada

![Simplified Architecture](./simplificado_svg.svg)

### Versão Atual

![Current Version](./versao_atual.svg)

## 🚀 Componentes Principais

### Lambda Functions

1. **lambda_kubernetes_pods.py** - Lista pods de um cluster específico
2. **lambda_kubernetes_logs.py** - Extrai logs de pods específicos
3. **lambda_post_k8s_logs.py** - Processa e envia logs para análise
4. **lambda_presigned_url.py** - Gera URLs pré-assinadas para S3
5. **lambda_sqs_k8s_logs.py** - Processa logs via SQS

### Serviços Core

- **aws_service.py** - Integração com serviços AWS (Secrets Manager)
- **kubernetes_service.py** - Operações com clusters Kubernetes
- **rancher_service.py** - Integração com Rancher API
- **utils.py** - Utilitários compartilhados

## 🛠️ Tecnologias Utilizadas

- **Python 3.12** - Linguagem principal
- **AWS Lambda** - Computação serverless
- **API Gateway** - Exposição de APIs REST
- **S3** - Armazenamento de logs
- **SQS** - Processamento assíncrono
- **Secrets Manager** - Gerenciamento de credenciais
- **Terraform** - Infraestrutura como código
- **Kubernetes** - Orquestração de containers
- **Rancher** - Gerenciamento de clusters

## 📦 Dependências

```txt
urllib3
requests
kubernetes
```

## 🔧 Configuração

### Pré-requisitos

1. **AWS CLI** configurado com as credenciais apropriadas
2. **Terraform** instalado (versão compatível)
3. **Python 3.12** ou superior
4. Acesso ao **Rancher** configurado
5. **kubeconfig** válido para os clusters

### Variáveis de Ambiente

O projeto utiliza as seguintes variáveis de ambiente (configuradas via Terraform):

- `sqs_fila_processar_logs` - Fila SQS para processamento de logs
- `region` - Região AWS
- `account` - ID da conta AWS
- `bucket_name` - Bucket S3 para armazenamento de logs

## 🚀 Deploy

### 1. Configuração da Infraestrutura

```bash
cd terraform
terraform init
terraform plan
terraform apply
```

### 2. Deploy das Lambda Functions

As funções Lambda são automaticamente deployadas via Terraform usando os módulos configurados.

## 📡 Endpoints da API

### Base URL

```
https://api-gateway-url/v1
```

### Endpoints Disponíveis

#### 1. Análise de Logs

```http
POST /analyze
```

#### 2. URL Pré-assinada

```http
GET /presignedurl
```

#### 3. Listagem de Pods

```http
GET /pods
```

#### 4. Logs v2

```http
GET /logsv2
```

## 🔐 Segurança

- **Autenticação**: Integração com AWS Secrets Manager para tokens do Rancher
- **Autorização**: Políticas IAM específicas para cada Lambda
- **Rede**: Configuração VPC para acesso seguro aos recursos
- **Criptografia**: Dados em trânsito e em repouso protegidos

## 📊 Monitoramento

O sistema inclui:

- Logs detalhados via CloudWatch
- Métricas de performance das Lambda functions
- Alertas configuráveis
- Rastreamento de erros e exceções

## 🔄 Fluxo de Dados

1. **Requisição** → API Gateway
2. **Processamento** → Lambda Function
3. **Autenticação** → Rancher via token do Secrets Manager
4. **Operação** → Cluster Kubernetes
5. **Armazenamento** → S3 (logs) / SQS (processamento assíncrono)
6. **Resposta** → Cliente

## 🧪 Testes

Para executar os testes localmente:

```bash
# Instalar dependências
pip install -r requirements.txt

# Executar testes (quando disponíveis)
python -m pytest tests/
```

## 📝 Logs e Debugging

Os logs são centralizados no CloudWatch e incluem:

- Informações de debug detalhadas
- Rastreamento de requisições
- Métricas de performance
- Alertas de erro

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova feature'`)
4. Push para a branch (`git push origin feature/nova-feature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto é propriedade da empresa e está sujeito às políticas internas de desenvolvimento.

## 🆘 Suporte

Para suporte técnico, entre em contato com a equipe de DevOps ou abra uma issue no repositório interno.

---

**Nota**: Este README foi gerado automaticamente baseado na análise do código fonte. Para informações mais detalhadas, consulte a documentação técnica específica de cada componente.
