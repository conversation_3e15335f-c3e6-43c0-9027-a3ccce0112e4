module "cloudin_kubernetes_list_pods" {
    source = "git::https://gitempresa.brasil/container-serverless/modules/lambda.git?ref=v1.3"

    aws_regiao_conta=var.aws_regiao_conta
    managed_policy_arns=[
        data.aws_iam_policy.lamda_vpc_policy.arn,
        data.aws_iam_policy.lambda_secret.arn,
    ]

    function_name="cpe-cloudin-kubernetes-list-pods"
    runtime="python3.12"
    handler="lambda_kubernetes_pods.lambda_handler"
    memory_size="256"
    timeout="30"

    # habilita acesso a vpc caso sua lambda precise acessar algum recurso dentro da porto ou da propria vpc
    configure_vpc_access=true
    cidr_block=var.cidr
}

module "cloudin_kubernetes_logsv2" {
    source = "git::https://gitempresa.brasil/container-serverless/modules/lambda.git?ref=v1.3"

    aws_regiao_conta=var.aws_regiao_conta
    managed_policy_arns=[
        data.aws_iam_policy.lamda_vpc_policy.arn,
        data.aws_iam_policy.lambda_secret.arn,
    ]

    function_name="cpe-cloudin-kubernetes-logsv2"
    runtime="python3.12"
    handler="lambda_kubernetes_lods.lambda_handler"
    memory_size="256"
    timeout="30"

    # habilita acesso a vpc caso sua lambda precise acessar algum recurso dentro da porto ou da propria vpc
    configure_vpc_access=true
    cidr_block=var.cidr
}

module "cloudin_kubernetes_analyze" {
    source = "git::https://gitempresa.brasil/container-serverless/modules/lambda.git?ref=v1.3"

    aws_regiao_conta=var.aws_regiao_conta
    managed_policy_arns=[
        data.aws_iam_policy.lamda_vpc_policy.arn,
        data.aws_iam_policy.lambda_secret.arn,
        "arn:aws:iam::1234IDACCOUNT:policy/sqs/cloudin_sqs_envia_logs_send_message"
    ]

    function_name="lamdba_post_k8s_logs"
    runtime="python3.12"
    handler="lambda_post_k8s_logs.lambda_handler"
    memory_size="256"
    timeout="30"

    # habilita acesso a vpc caso sua lambda precise acessar algum recurso dentro da porto ou da propria vpc
    configure_vpc_access=true
    cidr_block=var.cidr

    env = {
        "sqs_fila_processar_logs" = local.sqs_fila_processar_logs
        "region" = var.aws_regiao_conta
        "account" = data.aws_caller_identity.current.account_id
    }
}

module "lambda_processar_analise" {
    source = "git::https://gitempresa.brasil/container-serverless/modules/lambda.git?ref=v1.3"

    aws_regiao_conta=var.aws_regiao_conta
    managed_policy_arns=[
        data.aws_iam_policy.lamda_vpc_policy.arn,
        data.aws_iam_policy.lambda_secret.arn,
        aws_iam_policy.backstage_logs_analyze_logs.arn
    ]

    function_name="lambda_presigned_url"
    runtime="python3.12"
    handler="lambda_presigned_url.lambda_handler"
    memory_size="256"
    timeout="30"

    # habilita acesso a vpc caso sua lambda precise acessar algum recurso dentro da porto ou da propria vpc
    configure_vpc_access=true
    cidr_block=var.cidr

    env = {
        "bucket_name" = var.log_techstore_s3_bucket
    }
}

data "aws_iam_policy_document" "sqs_techstore_sendmessage_logs" {
    statement {
        effect = "Allow"

        actions = [
            "sqs:SendMessage"
        ]

        resources = [
            "arn:aws:s3:::${var.log_techstore_s3_bucket}",
            "arn:aws:s3:::${var.log_techstore_s3_bucket}/*"
        ]
    }
}

data "aws_iam_policy_document" "log_techstore_s3_bucket" {
    statement {
        effect = "Allow"

        actions = [
            "s3:GetObject",
            "s3:PutObject"
        ]

        resources = [
            "arn:aws:s3:::${var.log_techstore_s3_bucket}",
            "arn:aws:s3:::${var.log_techstore_s3_bucket}/*"
        ]
    }
}

resource "aws_iam_policy" "backstage_logs_analyze_logs" {
    name="backstage_logs_analyze_logs"
    path="/"
    description="permissoes para acessar o bucke de logs techstore backstage"

    policy = jsonencode({
        Version="2012-10-17"
        Statement=[
            {
                Effect="Allow"
                actions = [
                    "s3:GetObject",
                    "s3:PutObject"
                ]  
                
                resources = [
                    "arn:aws:s3:::${var.log_techstore_s3_bucket}",
                    "arn:aws:s3:::${var.log_techstore_s3_bucket}/*"
                ] 
            }
        ]
    })
}