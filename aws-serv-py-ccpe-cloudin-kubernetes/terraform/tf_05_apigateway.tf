module "aiops" {
    source = "git::https://gitempresa.prd.brasil/container-serverless/modules/api-gateway.git?ref=v0.4"

    api_name = "cpe_kubernetes"
    stage_name = "v1"
    aws_regiao_conta = var.aws_regiao_conta
    
    lambda_integrations = {
        ic={
             ### path here
        }
        cicd={
            ### path here
        }
        pods={
            ### path here
        }
        logsv2={
            ### path here
        }
        analyze={
            path="analyze"

            methods={
                post={
                    http_method="POST"
                    uri = module.cloudin_kubernetes_analyze.invoke_arn
                    function_name = module.cloudin_kubernetes_analyze.function_name
                    authorization = "NONE"
                }
            }
        }
        presignedurl={
            path="presignedurl"
            methods={
                get={
                    http_method="GET"
                    uri=module.lambda_presigned_url.invoke_arn
                    function_name=module.lambda_presigned_url.function_name
                    authorization="NONE"
                }
            }
        }
    }
}