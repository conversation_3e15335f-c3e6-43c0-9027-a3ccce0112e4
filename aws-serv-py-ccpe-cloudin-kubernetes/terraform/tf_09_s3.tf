resource "aws_s3_bucket" "logs_bucket" {
    bucket = var.log_techstore_s3_bucket
    acl = "private"

    tags = {
        Name = "logs-bucket"
    }
}

data "aws_iam_policy_document" "logs_bucket_policy" {
    statement {
        actions = ["s3:GetObject", "s3:PutObject"]
        resources = ["${aws_s3_bucket.logs_bucket.arn}/*"]

        principals {
            type = "AWS"
            identifiers = [aws_iam_role.lambda_role.arn]
        }
    }
}

resource "aws_s3_bucket_policy" "logs_bucket_policy" {
    bucket = aws_s3_bucket.logs_bucket.id
    policy = data.aws_iam_policy_document.logs_bucket_policy.json
}

resource "aws_iam_role" "lambda_role" {
    name = "lambda_role_name"

    assume_role_policy = jsonencode({
        Version= "2012-10-17"
        Statement =[
            {
                Effect = "Allow",
                Principa = {
                    Service = "lambda.amazonaws.com"
                },
                Action = "sts:AssumeRole"
            }
        ]
    })

    tags = {
        Name = "lambda_role_name"
    }
}

resource "aws_iam_policy" "lambda_s3_policy" {
    name = "lambda_s3_policy"
    description = "permissoes para a funcaoi lambda acessar o bucket s3"

    policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
            {
                Effect = "Allow"
                Action = [
                    "s3:GetObject",
                    "s3:PutObject"
                ],
                Resource = [
                    "${aws_s3_bucket.logs_bucket.arn}/*"
                ]
            }
        ]
    })
}

resource "aws_iam_role_policy_attachment" "lambda_s3_policy_attachment" {
    role = aws_iam_role.lambda_role.name
    policy_arn = aws_iam_policy.lambda_s3_policy.arn
}