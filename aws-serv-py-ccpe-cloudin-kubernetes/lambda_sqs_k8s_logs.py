import json
import boto3
import genial
import os
from utils import response
import kubernetes_service
import rancher_service
import aws_service
from kubernetes.client.rest import ApiException

sqs = boto3.client('sqs')
TOKEN = aws_service.get_token()
s3_client = boto3.client('s3')
# bucket_name = 'cloudin-analyze-logs'
bucket_name = os.environ.get("bucket_name", "cloudin-analyze-logs")

def lambda_handler(event, context):
    print(json.dumps(event))
    
    processed_logs = []

    for record in event['Records']:
        message_body = json.loads(record['body'])
        print(message_body)

        s3_key = message_body['s3_key']
        cluster_id = rancher_service.obter_cluster_id(TOKEN, message_body['cluster'])
        kubeconfig = rancher_service.download_kubeconfig(TOKEN, cluster_id)

        if not kubeconfig:
            return response(404, {'error': 'cluster nao encontrado'})
        
        kubernetes_service.configure_kubernetes_client(kubeconfig)

        try:
            logs = kubernetes_service.get_pod_logs(message_body['namespace'], message_body['pod'])
        except ApiException as e:
            if e.status == 404:
                return response(404, {'error': f'pod "{message_body['pod']}" nao encontrado no namespace "{message_body["namespace"]}"'})
            else:
                return response(500, {'error': 'Erro ao obter logs', 'details': str(e)})

        if logs is None:
            return response(404, {'error': 'logs nao encontrados.'})

        processed = process_log_chunks(logs)
        if processed:
            processed_logs.append((s3_key, processed))

        print(processed_logs)

        for key, logs in processed_logs:
            save_analysis_to_s3(key, logs)

    return {'statusCode':200, 'body': f'analise salva em s3://{bucket_name}/{s3_key}'}


def process_log_chunks(logs, chunk_size=100000):
    try:
        last = len(logs) - chunk_size
        chunk = logs[last:]
        logsIA = genial.genial_call(chunk, variante='logs')
        return logsIA['message']['content']

    except Exception as e:
        print(e)
        return f'erro here in logs. {e}'

def save_analysis_to_s3(key, logs):
    try:
        s3_client.put_object(
            Bucket=bucket_name,
            Key=f'{key}',
            Body=logs,
            ContentType='text/html'
        )
    except Exception as e:
        print(f"error here {e}")
        raise