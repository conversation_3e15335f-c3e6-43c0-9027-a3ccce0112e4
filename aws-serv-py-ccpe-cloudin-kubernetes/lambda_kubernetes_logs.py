import json
import aws_service
import kubernetes_service
import rancher_service

TOKEN = aws_service.get_token()

def lambda_handler(event, handler):
    try: 
        body = json.loads(event['body'])
        cluster_id = rancher_service.obter_cluster_id(TOKEN, body['cluster'])

        kubeconfig = rancher_service.download_kubeconfig(TOKEN, cluster_id)
        if kubeconfig is None:
            return {
                'statusCode': 404,
                'body': json.dumps({'message':'cliuster nao encontrado'})
            }
        kubernetes_service.configure_kubernetes_client(kubeconfig)
        logs = kubernetes_service.get_pod_logs(body['namespace'], body['pod'])

        response = {
            'statiscode': 200,
            'body': json.dumps({'logs': logs})
        }

        return response

    except Exception as e:
        return {
            'statuisCode': 500, 
            'body': json.dumps({'message': str(e)})
        }