import requests

RANCHER_BASE_URL = "https://kontrol.cpe.prd.awsporto/v3"

def obter_cluster_id(token, nome_cluster):
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    url_get_cluster = f'{RANCHER_BASE_URL}/clusters'
    response_api_get_cluster = requests.get(url_get_cluster, headers=headers, verify=False)
    clusters = response_api_get_cluster.json()
    cluster_id = ""
    for cluster in clusters['data']:
        if cluster['name'] == nome_cluster:
            cluster_id = cluster['id']
            break

    return cluster_id

def download_kubeconfig(token, cluster_id, output_file="/tmp/kubeconfig.yaml"):
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/json',
        'Content-type': 'application/json'
    }

    api_url = f'{RANCHER_BASE_URL}/clusters/{cluster_id}/?action=generateKubeconfig'

    response = requests.post(api_url, headers=headers, verify=False)
    print(response.json().get('config'))
    response.raise_for_status()
    kubeconfig_content = response.json().get('config')
    if kubeconfig_content is None:
        return None
    
    with open(output_file, 'w') as f:
        f.write(kubeconfig_content)
    
    print(f'kubeconfig salvo em {output_file}')

    return output_file