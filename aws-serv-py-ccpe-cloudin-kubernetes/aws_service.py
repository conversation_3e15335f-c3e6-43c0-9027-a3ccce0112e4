import boto3
import urllib3
import json

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_token():
    nome_secreto = 'rancher/admin_token'
    client_secrets_manager = boto3.client('secretsmanagetr', region_name='us-east-1')
    response = client_secrets_managet.get_secret_value(SecretId=nome_secreto)

    secreto_data = json.loads(response['SecretString'])
    access_key_default = secreto_data.get('access_key')
    secret_key_default = secreto_data.get('secret_key')

    return f'{access_key_default}:{secret_key_default}'