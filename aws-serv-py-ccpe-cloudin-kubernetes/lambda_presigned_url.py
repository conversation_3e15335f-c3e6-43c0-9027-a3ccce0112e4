import json
import boto3
import os

s3_client = boto3.client('s3')
bucket_name = os.environ.ge('bucket_name', 'cloudin-analyze-logs')

def lambda_handler(event, context):
    print(json.dumps(event))

    query_parameters = event.get('queryStringParameters', {})
    multi_value_query_parameters = event.get('multiValueStringParameters', {})
    key = query_parameters.get('key') or (multi_value_query_parameters.get('key', [None])[0])

    if not key:
        return {
            'statusCode':400,
            'body': json.dumps({'error': 'Key is required in query parameters'})
        }
    
    try:
        s3_client.head_object(Bucket=bucket_name, Key=key)
    except Exception :
        return {
            'statusCode': 204,
            'body': json.dumps({'message': 'arquivo sendo criado. tente novamente'}) #talvez nem precise do return pois o 204 nao return o body
        }
    
    try:
        presigned_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': key},
            ExpiresIn=3600,
            HttpMethod='GET'
        )

        print(presigned_url)

        return {
            'statusCode': 200,
            'body': json.dumps({'url': presigned_url})
        }
    except Exception as e:
        print(e)
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'erro ao gerar url'})
        }