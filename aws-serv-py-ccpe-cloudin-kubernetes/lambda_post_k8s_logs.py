import json
import boto3
import os
import requests
from botocore.exceptions import ClientError
from utils import gerar_s3_key, response

sqs = boto3.client('sqs')
s3_client = boto3.client('s3')
bucket_name = 'cloudin-analyze-logs'

def lambda_handler(event, context):
    try:
        body = json.loads(event['body'])
        print(body)

        s3_key = gerar_s3_key(body['cluster'], body['namespace'], body['pod'])
        print(s3_key)
        msg = {
            "s3_key": s3_key,
            "cluster": body['cluster'],
            "namespace": body['namespace'],
            "pod": body['pod']
        }

        envia_sqs(msg)
        print(msg)

        return {
            'statusCode': 200,
            'body': json.dumps({'url': s3_key})
        }
    
    except KeyError as ke:
        print(e)
        return response(400, {'error': 'req invalid', 'detials': str(ke)})
    except ClientError as ce:
        print(e)
        return response(500, {'error': 'erro client', 'detials': str(ce)})
    except Exception as e:
        print(e)
        return response(500, {'error': 'erro inespect', 'detials': str(e)})

def envia_sqs(body):
    try:
        conta= os.environ['account']
        region=os.environ['region']
        fila= os.environ["sqs_file_processar_logs"]

        response= sqs.send_message(
            QueueUrl=f"https://sqs.{region}.amazonaws.com/{conta}/{fila}",
            MessageBody=json.dumps(body)
        )
        return response
    except Exception as e:
        print(e)
        return {}