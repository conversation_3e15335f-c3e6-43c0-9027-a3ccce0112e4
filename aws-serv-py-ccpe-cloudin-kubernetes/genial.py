import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def genial_auth():
    data = {
        "grant_type":"client_credentials"
    }

    headers = {
        'Authorization': 'Basic 1241213421w412341234c',
        'content-type': 'application/x-www-formo-urlencoded'
    }

    url = 'https://apicloud.dev.porto.brasil/oauth/v2/access-token'
    res = requests.post(url,
    headers=headers,
    data=data, verify=False)
    token = json.loads(res.text)
    return token['access_token']

def genial_call(prompt):
    return genial_call(prompt=prompt, variante='documentos')

def genial_call(prompt=None, variante="logs")
    token = genial_auth()

    url = 'https://apicloud.dev.braisl/corp/genial/1234124iuj12h43214-4325325-3453/chat'
    
    headers = {
        'content-type':'application/json',
        'authorization':f'Bearer {token}'
    }

    data=  json.dumps({
        'varianteName': variante,
        'prompt':prompt
    })

    res =  request.post(url,
    headers= headers,
    data=data, verify=False)
    return json.loads(res.text)