<svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .aws-orange { fill: #FF9900; }
      .aws-blue { fill: #232F3E; }
      .text-white { fill: white; font-family: Arial, sans-serif; font-size: 12px; }
      .text-black { fill: #232F3E; font-family: Arial, sans-serif; font-size: 11px; }
      .service-box { fill: #FF9900; stroke: #232F3E; stroke-width: 2; rx: 8; }
      .external-service { fill: #4CAF50; stroke: #2E7D32; stroke-width: 2; rx: 8; }
      .data-flow { stroke: #FF9900; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .external-flow { stroke: #4CAF50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#FF9900" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="#F5F5F5"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#232F3E">
    Sistema de Análise de Logs Kubernetes - AWS Architecture
  </text>
  
  <!-- AWS Cloud Border -->
  <rect x="50" y="60" width="1100" height="720" fill="none" stroke="#FF9900" stroke-width="3" stroke-dasharray="10,5" rx="10"/>
  <text x="100" y="85" font-size="16" font-weight="bold" fill="#FF9900">AWS Cloud</text>
  
  <!-- External Services -->
  <rect x="20" y="150" width="150" height="80" class="external-service"/>
  <text x="95" y="175" text-anchor="middle" class="text-white" font-weight="bold">Rancher</text>
  <text x="95" y="190" text-anchor="middle" class="text-white">Kubernetes</text>
  <text x="95" y="205" text-anchor="middle" class="text-white">Management</text>
  
  <rect x="20" y="250" width="150" height="60" class="external-service"/>
  <text x="95" y="275" text-anchor="middle" class="text-white" font-weight="bold">Genial AI</text>
  <text x="95" y="290" text-anchor="middle" class="text-white">API</text>
  
  <!-- API Gateway -->
  <rect x="250" y="120" width="140" height="100" class="service-box"/>
  <text x="320" y="145" text-anchor="middle" class="text-white" font-weight="bold">API Gateway</text>
  <text x="320" y="160" text-anchor="middle" class="text-white">cpe_kubernetes</text>
  <text x="320" y="180" text-anchor="middle" class="text-black" font-size="10">Endpoints:</text>
  <text x="320" y="195" text-anchor="middle" class="text-black" font-size="10">• /analyze</text>
  <text x="320" y="210" text-anchor="middle" class="text-black" font-size="10">• /presignedurl</text>
  
  <!-- Lambda Functions -->
  <rect x="450" y="80" width="120" height="60" class="service-box"/>
  <text x="510" y="105" text-anchor="middle" class="text-white" font-weight="bold">Lambda</text>
  <text x="510" y="120" text-anchor="middle" class="text-white">List Pods</text>
  
  <rect x="600" y="80" width="120" height="60" class="service-box"/>
  <text x="660" y="105" text-anchor="middle" class="text-white" font-weight="bold">Lambda</text>
  <text x="660" y="120" text-anchor="middle" class="text-white">Get Logs</text>
  
  <rect x="450" y="180" width="120" height="60" class="service-box"/>
  <text x="510" y="205" text-anchor="middle" class="text-white" font-weight="bold">Lambda</text>
  <text x="510" y="220" text-anchor="middle" class="text-white">Post K8s Logs</text>
  
  <rect x="600" y="180" width="120" height="60" class="service-box"/>
  <text x="660" y="205" text-anchor="middle" class="text-white" font-weight="bold">Lambda</text>
  <text x="660" y="220" text-anchor="middle" class="text-white">Presigned URL</text>
  
  <rect x="450" y="280" width="120" height="60" class="service-box"/>
  <text x="510" y="305" text-anchor="middle" class="text-white" font-weight="bold">Lambda</text>
  <text x="510" y="320" text-anchor="middle" class="text-white">SQS Processor</text>
  
  <!-- SQS Queue -->
  <rect x="800" y="220" width="140" height="80" class="service-box"/>
  <text x="870" y="245" text-anchor="middle" class="text-white" font-weight="bold">SQS Queue</text>
  <text x="870" y="260" text-anchor="middle" class="text-white">cloudin_sqs_</text>
  <text x="870" y="275" text-anchor="middle" class="text-white">envia_logs</text>
  <text x="870" y="290" text-anchor="middle" class="text-black" font-size="10">Log Processing</text>
  
  <!-- S3 Bucket -->
  <rect x="800" y="350" width="140" height="100" class="service-box"/>
  <text x="870" y="375" text-anchor="middle" class="text-white" font-weight="bold">S3 Bucket</text>
  <text x="870" y="390" text-anchor="middle" class="text-white">cloudin-</text>
  <text x="870" y="405" text-anchor="middle" class="text-white">analyze-logs</text>
  <text x="870" y="425" text-anchor="middle" class="text-black" font-size="10">Stores:</text>
  <text x="870" y="440" text-anchor="middle" class="text-black" font-size="10">• Raw logs</text>
  
  <!-- Secrets Manager -->
  <rect x="250" y="400" width="140" height="80" class="service-box"/>
  <text x="320" y="425" text-anchor="middle" class="text-white" font-weight="bold">Secrets Manager</text>
  <text x="320" y="445" text-anchor="middle" class="text-white">rancher/</text>
  <text x="320" y="460" text-anchor="middle" class="text-white">admin_token</text>
  
  <!-- IAM Role -->
  <rect x="450" y="400" width="120" height="60" class="service-box"/>
  <text x="510" y="425" text-anchor="middle" class="text-white" font-weight="bold">IAM Role</text>
  <text x="510" y="440" text-anchor="middle" class="text-white">Lambda Role</text>
  
  <!-- Flow Lines -->
  <!-- User to API Gateway -->
  <line x1="200" y1="170" x2="250" y2="170" class="data-flow"/>
  
  <!-- API Gateway to Lambdas -->
  <line x1="390" y1="150" x2="450" y2="110" class="data-flow"/>
  <line x1="390" y1="160" x2="600" y2="110" class="data-flow"/>
  <line x1="390" y1="180" x2="450" y2="210" class="data-flow"/>
  <line x1="390" y1="190" x2="600" y2="210" class="data-flow"/>
  
  <!-- Lambda to SQS -->
  <line x1="570" y1="210" x2="800" y2="250" class="data-flow"/>
  
  <!-- SQS to Lambda Processor -->
  <line x1="800" y1="280" x2="570" y2="310" class="data-flow"/>
  
  <!-- Lambda to S3 -->
  <line x1="570" y1="220" x2="800" y2="380" class="data-flow"/>
  <line x1="720" y1="210" x2="800" y2="380" class="data-flow"/>
  <line x1="570" y1="320" x2="800" y2="400" class="data-flow"/>
  
  <!-- Lambda to Secrets Manager -->
  <line x1="450" y1="200" x2="390" y2="430" class="data-flow"/>
  
  <!-- Lambda to Rancher -->
  <line x1="450" y1="110" x2="170" y2="190" class="external-flow"/>
  <line x1="600" y1="110" x2="170" y2="190" class="external-flow"/>
  
  <!-- Lambda to Genial AI -->
  <line x1="450" y1="340" x2="170" y2="280" class="external-flow"/>
  
  <!-- Flow Labels -->
  <text x="210" y="165" class="text-black" font-size="10">HTTP Requests</text>
  <text x="420" y="140" class="text-black" font-size="10">Invoke</text>
  <text x="700" y="240" class="text-black" font-size="10">Send Message</text>
  <text x="700" y="300" class="text-black" font-size="10">Poll</text>
  <text x="700" y="360" class="text-black" font-size="10">Store Logs</text>
  <text x="300" y="180" class="text-black" font-size="10">Get Token</text>
  <text x="300" y="150" class="text-black" font-size="10">K8s API</text>
  <text x="300" y="310" class="text-black" font-size="10">AI Analysis</text>
  
  <!-- Process Flow Description -->
  <rect x="80" y="550" width="1040" height="200" fill="white" stroke="#232F3E" stroke-width="1" rx="5"/>
  <text x="600" y="575" text-anchor="middle" font-size="14" font-weight="bold" fill="#232F3E">
    Fluxo de Processamento
  </text>
  
  <text x="100" y="600" class="text-black">
    1. Cliente faz requisição para API Gateway (/analyze endpoint)
  </text>
  <text x="100" y="620" class="text-black">
    2. Lambda "Post K8s Logs" recebe request com cluster, namespace e pod
  </text>
  <text x="100" y="640" class="text-black">
    3. Lambda busca token do Rancher no Secrets Manager
  </text>
  <text x="100" y="660" class="text-black">
    4. Lambda gera chave S3 e envia mensagem para SQS com metadata
  </text>
  <text x="100" y="680" class="text-black">
    5. Lambda "SQS Processor" é acionado pela mensagem SQS
  </text>
  <text x="100" y="700" class="text-black">
    6. Processor conecta no Rancher, baixa kubeconfig e extrai logs do pod
  </text>
  <text x="100" y="720" class="text-black">
    7. Logs são enviados para Genial AI para análise e resultado salvo no S3
  </text>
  <text x="100" y="740" class="text-black">
    8. Cliente usa /presignedurl para acessar análise processada no S3
  </text>
</svg>