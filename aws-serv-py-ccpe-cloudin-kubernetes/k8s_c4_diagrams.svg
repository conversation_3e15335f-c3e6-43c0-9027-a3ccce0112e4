<svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="personGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="systemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891B2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="externalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#DC2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EA580C;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="#0F172A"/>
  
  <!-- Title -->
  <text x="600" y="40" text-anchor="middle" fill="#F1F5F9" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Kubernetes Log Analytics Platform - Context Diagram
  </text>
  
  <!-- System Boundary -->
  <rect x="400" y="300" width="400" height="200" rx="15" fill="none" stroke="#334155" stroke-width="2" stroke-dasharray="5,5" opacity="0.7"/>
  <text x="600" y="290" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    K8s Log Analytics Platform
  </text>
  
  <!-- Core System -->
  <g filter="url(#shadow)">
    <rect x="450" y="350" width="300" height="100" rx="10" fill="url(#systemGradient)"/>
    <text x="600" y="385" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
      Kubernetes Log Analytics
    </text>
    <text x="600" y="405" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">
      AI-powered log analysis system
    </text>
    <text x="600" y="420" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">
      for multi-cluster environments
    </text>
  </g>
  
  <!-- Users -->
  <g filter="url(#shadow)">
    <!-- DevOps Engineer -->
    <circle cx="150" cy="200" r="40" fill="url(#personGradient)"/>
    <text x="150" y="205" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      DevOps
    </text>
    <text x="150" y="260" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">
      Platform engineer requiring
    </text>
    <text x="150" y="275" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">
      deep insights into K8s workloads
    </text>
    
    <!-- SRE -->
    <circle cx="150" cy="400" r="40" fill="url(#personGradient)"/>
    <text x="150" y="405" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      SRE
    </text>
    <text x="150" y="460" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">
      Site reliability engineer
    </text>
    <text x="150" y="475" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">
      monitoring production clusters
    </text>
    
    <!-- Developer -->
    <circle cx="150" cy="600" r="40" fill="url(#personGradient)"/>
    <text x="150" y="605" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      Developer
    </text>
    <text x="150" y="660" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">
      Application developer debugging
    </text>
    <text x="150" y="675" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">
      microservices in K8s
    </text>
  </g>
  
  <!-- External Systems -->
  <g filter="url(#shadow)">
    <!-- Rancher -->
    <rect x="850" y="150" width="120" height="80" rx="8" fill="url(#externalGradient)"/>
    <text x="910" y="180" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      Rancher
    </text>
    <text x="910" y="195" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">
      Management
    </text>
    <text x="910" y="210" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      Multi-cluster K8s platform
    </text>
    
    <!-- K8s Clusters -->
    <rect x="850" y="270" width="120" height="80" rx="8" fill="url(#externalGradient)"/>
    <text x="910" y="300" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      K8s Clusters
    </text>
    <text x="910" y="320" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      Production EKS clusters
    </text>
    <text x="910" y="335" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      running workloads
    </text>
    
    <!-- Genial AI -->
    <rect x="850" y="390" width="120" height="80" rx="8" fill="url(#externalGradient)"/>
    <text x="910" y="420" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      Genial AI
    </text>
    <text x="910" y="435" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">
      Platform
    </text>
    <text x="910" y="450" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      AI service for log analysis
    </text>
    
    <!-- AWS Bedrock -->
    <rect x="850" y="510" width="120" height="80" rx="8" fill="url(#externalGradient)"/>
    <text x="910" y="540" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      AWS Bedrock
    </text>
    <text x="910" y="560" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      Alternative AI service for
    </text>
    <text x="910" y="575" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      cost optimization
    </text>
    
    <!-- Backstage -->
    <rect x="850" y="630" width="120" height="80" rx="8" fill="url(#externalGradient)"/>
    <text x="910" y="660" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      Backstage IDP
    </text>
    <text x="910" y="680" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      Internal Developer Platform
    </text>
    <text x="910" y="695" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
      for service catalog
    </text>
  </g>
  
  <!-- Relationships -->
  <!-- Users to System -->
  <g stroke="#10B981" stroke-width="2" fill="none" opacity="0.8">
    <path d="M 190 200 Q 350 250 450 380" marker-end="url(#arrowhead)"/>
    <path d="M 190 400 Q 350 400 450 400" marker-end="url(#arrowhead)"/>
    <path d="M 190 600 Q 350 550 450 420" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- System to External -->
  <g stroke="#F59E0B" stroke-width="2" fill="none" opacity="0.8">
    <path d="M 750 380 Q 800 300 850 190" marker-end="url(#arrowhead)"/>
    <path d="M 750 400 Q 800 350 850 310" marker-end="url(#arrowhead)"/>
    <path d="M 750 420 Q 800 430 850 430" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Backstage to System -->
  <g stroke="#8B5CF6" stroke-width="2" fill="none" opacity="0.8">
    <path d="M 850 670 Q 750 550 750 450" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Arrow markers -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#10B981" opacity="0.8"/>
    </marker>
  </defs>
  
  <!-- Relationship Labels -->
  <g fill="#CBD5E1" font-family="Arial, sans-serif" font-size="10">
    <text x="300" y="240">Analyzes cluster logs</text>
    <text x="300" y="400">Monitors incidents</text>
    <text x="300" y="560">Debugs applications</text>
    
    <text x="800" y="240">Retrieves kubeconfig</text>
    <text x="800" y="340">Fetches pod logs</text>
    <text x="800" y="430">Processes logs with AI</text>
    
    <text x="780" y="580">Integrates operational data</text>
  </g>
  
  <!-- Legend -->
  <g transform="translate(50, 700)">
    <rect x="0" y="0" width="300" height="80" rx="5" fill="#1E293B" stroke="#334155" opacity="0.9"/>
    <text x="150" y="20" text-anchor="middle" fill="#F1F5F9" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      Legend
    </text>
    
    <circle cx="20" cy="40" r="8" fill="url(#personGradient)"/>
    <text x="35" y="45" fill="#CBD5E1" font-family="Arial, sans-serif" font-size="10">Person</text>
    
    <rect x="80" y="32" width="16" height="16" rx="2" fill="url(#systemGradient)"/>
    <text x="105" y="42" fill="#CBD5E1" font-family="Arial, sans-serif" font-size="10">System</text>
    
    <rect x="150" y="32" width="16" height="16" rx="2" fill="url(#externalGradient)"/>
    <text x="175" y="42" fill="#CBD5E1" font-family="Arial, sans-serif" font-size="10">External</text>
    
    <line x1="20" y1="60" x2="35" y2="60" stroke="#10B981" stroke-width="2"/>
    <text x="40" y="64" fill="#CBD5E1" font-family="Arial, sans-serif" font-size="10">Relationship</text>
  </g>
</svg>