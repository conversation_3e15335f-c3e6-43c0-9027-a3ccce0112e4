import urllib3
from kubernetes import client, config
import json
import os

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

v1 = None

def configure_kubernetes_client(kuberconfig_path):
    global v1
    if not kuberconfig_path or not os.path.isfile(kuberconfig_path):
        print('kubeconfig is not exit')
        return
    
    try:
        config.load_kube_config(config_file=kubeconfig_path)
        v1 = client.CoreV1Api()
    except Exception as e :
        print(e)

def list_all_pods():
    return v1.list_pod_for_all_namespaces(watch=False)

def list_pods_with_namespaces(app_name):
    try:
        pods = list_all_pods()
    except Runtime as e:
        print(e)
        return json.dumps({'message':'pods nao listados'})
    
    result = []
    for pod in pods.items:
        try:
            if app_name in pod.metadata.name:
                namespace = pod.metadata.namespace
                pod_name = pod.metadata.name
                result.append({
                    'namespace': namespace,
                    'pod': pod_name
                })
        except Exception as e:
            print(e)
    if not result:
        return json.dumps({'message':'nenhum pod encontrado com o nome especificado'})

    return json.dumps({
        'pods': result,
        indent=2
    })

def get_pod_logs(namespace, pod_name, container_name=None):
    if container_name:
        logs = v1.read_namespace_pod_log(name=pod_namem, namespace=namespace, container=container_name)
    else:
        logs = v1.read_namespace_pod_log(name=pod_namem, namespace=namespace)

    return logs

def get_app_logs(namespace, application):
    if v1 is None:
        raise RuntimeError('client nao config')

    pods = v1.list_namespace_pod(namespace=namespace)
    logs=""

    for pod in pods.items:
        if not applicatino in pod.metadata.name:
            continue
        else:
            pod_name = pod.metadata.name
            logs += v.read_namespace_pod_logs(name=pod_name, namespace=namespace)

    return logs